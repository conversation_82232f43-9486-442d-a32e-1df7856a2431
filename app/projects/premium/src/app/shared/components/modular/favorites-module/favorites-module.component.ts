import { Component, Input, OnInit } from '@angular/core';
import { combineLatest, Observable, of } from 'rxjs';
import { IPreview } from '../../../../models/preview';
import { AsyncPipe } from '@angular/common';
import { RouterLink } from '@angular/router';
import { ArticlePreviewComponent } from '../../article-preview/article-preview.component';
import { GalleryCardComponent } from '../../gallery-card/gallery-card.component';
import { FavoritesService } from '../../../../services/favorites/favorites.service';
import { map, shareReplay, startWith, tap } from 'rxjs/operators';

@Component({
  selector: 'favorites-module',
  templateUrl: './favorites-module.component.html',
  styleUrls: ['./favorites-module.component.css'],
  imports: [
    AsyncPipe,
    RouterLink,
    ArticlePreviewComponent,
    GalleryCardComponent,
  ],
  standalone: true,
})
export class FavoritesModuleComponent implements OnInit {
  @Input() title = '';

  @Input() isGirlsVisible = false;
  @Input() isGirlInfosVisible = false;
  @Input() isVideosVisible = false;
  @Input() isImagesVisible = false;
  @Input() pageSize = 4;

  $girls: Observable<IPreview[]>;
  $girlInfos: Observable<IPreview[]>;
  $videos: Observable<IPreview[]>;
  $images: Observable<IPreview[]>;
  $hasFavorites: Observable<boolean>;
  $loading: Observable<boolean>;

  constructor(private favoritesService: FavoritesService) {}

  ngOnInit(): void {
    this.$girls = this.isGirlsVisible
      ? this.favoritesService.getPreviews('girl', { pageSize: this.pageSize }).pipe(shareReplay(1))
      : of([]);

    this.$girlInfos = this.isGirlInfosVisible
      ? this.favoritesService.getPreviews('girl-infos', {
          pageSize: this.pageSize,
        }).pipe(shareReplay(1))
      : of([]);

    this.$videos = this.isVideosVisible
      ? this.favoritesService.getPreviews('video', { pageSize: this.pageSize }).pipe(tap(v => console.log(v)), shareReplay(1))
      : of([]);

    this.$images = this.isImagesVisible
      ? this.favoritesService.getPreviews('image', { pageSize: this.pageSize }).pipe(tap(v => console.log(v)), shareReplay(1))
      : of([]);

    this.$hasFavorites = combineLatest([
      this.$girls,
      this.$girlInfos,
      this.$videos,
      this.$images,
    ]).pipe(
      map(([girls, girlInfos, videos, images]) => {
        const girlsLength = girls?.length || 0;
        const girlInfosLength = girlInfos?.length || 0;
        const videosLength = videos?.length || 0;
        const imagesLength = images?.length || 0;

        const totalFavorites = girlsLength + girlInfosLength + videosLength + imagesLength;
        return totalFavorites > 0;
      }),
    );

    this.$loading = this.$hasFavorites.pipe(
      map(() => false),
      startWith(true),
    );
  }
}
