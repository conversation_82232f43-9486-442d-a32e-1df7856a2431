<app-article-preview
  [autoSize]="autoFill"
  [focalPoint]="focalPoint"
  [imageRatio]="localImageRatio"
  [image]="image || previewDataImage"
  [imageAlt]="imageAlt || name"
  [videoPosterAlt]="name"
  [inlineVideo]="inlineVideo"
  [meta]="meta"
  [new]="isNew"
  [nexxID]="nexxID"
  [ngClass]="{
    'h-full flex-1 overflow-hidden': !autoFill,
    'aspect-2/1':
      overwriteImageRatio &&
      (overwriteImageRatio === '2 / 1' || overwriteImageRatio === '2/1'),
  }"
  [withoutHoverEffect]="withoutCard"
>
</app-article-preview>

@if (!withoutCard) {
  @if (!withoutInfos) {
    <div class="p-6 flex w-full flex-col overflow-hidden">
      @if (title === "Coverstar") {
        <p class="h4 mb-3 lg:mb-4 text-gray-400">
          {{ title }}
          {{ month | month }}
          {{ year }}
        </p>
      }
      @if (title === "Playmate des Monats") {
        <p class="h4 mb-3 lg:mb-4 text-gray-400">
          Miss
          {{ month | month }}
          {{ year }}
        </p>
      }
      @if (title !== "Playmate des Monats" && title !== "Coverstar") {
        <p class="h4 mb-3 lg:mb-4 text-gray-400">
          {{ title }}
        </p>
      }

      @if (name) {
        <p
          class="text-lg lg:text-2xl font-inter font-normal line-clamp-2 min-h-16"
          [innerHTML]="name"
        ></p>
      }
    </div>
  }

  @if ((accService.Subscribed | async) && favoriteType && favoriteId) {
    <app-favorite-star
      [paywallImages]="paywallImages() || []"
      [type]="favoriteType"
      [id]="favoriteId"
      class="absolute top-0 right-0"
    ></app-favorite-star>
  }

  @if (accService.subscriptionType() === "all-access" && isAAContent) {
    <app-lock-badge
      class="absolute top-4 right-4"
      [paywallImages]="paywallImages() || []"
    ></app-lock-badge>
  }
}
